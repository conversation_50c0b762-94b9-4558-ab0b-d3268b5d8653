{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/HeroSection.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\n\nconst HeroSection = () => {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 to-blue-100 py-16 lg:py-24 overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-20 h-20 bg-blue-300 rounded-full\"></div>\n        <div className=\"absolute top-32 right-20 w-16 h-16 bg-green-300 rounded-full\"></div>\n        <div className=\"absolute bottom-20 left-1/4 w-12 h-12 bg-orange-300 rounded-full\"></div>\n        <div className=\"absolute bottom-32 right-1/3 w-24 h-24 bg-purple-300 rounded-full\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <div className=\"text-center lg:text-left\">\n            <h1 className=\"text-4xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight\">\n              Học Online{' '}\n              <span className=\"text-blue-600\">Chăm Sóc Sức Khỏe</span>{' '}\n              Từ Chuyên Gia\n            </h1>\n            \n            <p className=\"text-lg lg:text-xl text-gray-600 mb-8 leading-relaxed\">\n              Nền tảng học trực tuyến hàng đầu về chăm sóc sức khỏe. \n              Khóa học chất lượng cao từ các chuyên gia y tế uy tín, \n              chứng chỉ được công nhận.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <Link\n                href=\"/khoa-hoc\"\n                className=\"bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 text-center\"\n              >\n                Khám Phá Khóa Học\n              </Link>\n              \n              <Link\n                href=\"/gioi-thieu\"\n                className=\"border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors duration-200 text-center\"\n              >\n                Tìm Hiểu Thêm\n              </Link>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-6 mt-12 pt-8 border-t border-gray-200\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl lg:text-3xl font-bold text-blue-600 mb-1\">1000+</div>\n                <div className=\"text-sm text-gray-600\">Học viên</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl lg:text-3xl font-bold text-green-600 mb-1\">50+</div>\n                <div className=\"text-sm text-gray-600\">Khóa học</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl lg:text-3xl font-bold text-orange-600 mb-1\">20+</div>\n                <div className=\"text-sm text-gray-600\">Chuyên gia</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Hero Image */}\n          <div className=\"relative\">\n            <div className=\"relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center\">\n                <div className=\"text-center text-white\">\n                  <div className=\"w-24 h-24 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-12 h-12\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                    </svg>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-2\">Chất Lượng Đảm Bảo</h3>\n                  <p className=\"text-blue-100\">Khóa học được thiết kế bởi chuyên gia</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Floating Cards */}\n            <div className=\"absolute -top-6 -left-6 bg-white p-4 rounded-xl shadow-lg\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n                  </svg>\n                </div>\n                <div>\n                  <div className=\"font-semibold text-sm\">Chứng chỉ uy tín</div>\n                  <div className=\"text-xs text-gray-500\">Được công nhận</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"absolute -bottom-6 -right-6 bg-white p-4 rounded-xl shadow-lg\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 14l9-5-9-5-9 5 9 5z\"/>\n                    <path d=\"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"/>\n                  </svg>\n                </div>\n                <div>\n                  <div className=\"font-semibold text-sm\">Học linh hoạt</div>\n                  <div className=\"text-xs text-gray-500\">24/7 trực tuyến</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAkE;wCACnE;sDACX,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAAyB;wCAAI;;;;;;;8CAI/D,8OAAC;oCAAE,WAAU;8CAAwD;;;;;;8CAMrE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAID,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoD;;;;;;8DACnE,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqD;;;;;;8DACpE,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAsD;;;;;;8DACrE,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAY,MAAK;wDAAe,SAAQ;kEACrD,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;8CAMnC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAyB,MAAK;oDAAe,SAAQ;8DAClE,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;;sEACjE,8OAAC;4DAAK,GAAE;;;;;;sEACR,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;0DAGZ,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;uCAEe", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/CourseSection.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\n\ninterface Course {\n  id: string;\n  title: string;\n  description: string;\n  instructor: string;\n  duration: string;\n  students: number;\n  price: string;\n  originalPrice?: string;\n  image: string;\n  category: string;\n  rating: number;\n}\n\nconst courses: Course[] = [\n  {\n    id: '1',\n    title: '<PERSON>ă<PERSON>óc <PERSON> Khỏe Cơ Bản',\n    description: '<PERSON><PERSON><PERSON><PERSON> học cung cấp kiến thức nền tảng về chăm sóc sức khỏe hàng ngày, dinh dưỡng và phòng bệnh.',\n    instructor: 'BS. Nguyễn Văn A',\n    duration: '8 tuần',\n    students: 1250,\n    price: '1.500.000đ',\n    originalPrice: '2.000.000đ',\n    image: '/assets/course1.jpg',\n    category: 'Sức khỏe tổng quát',\n    rating: 4.8\n  },\n  {\n    id: '2',\n    title: 'Dinh Dưỡng Lâm Sàng',\n    description: 'Tìm hiểu về dinh dưỡng trong điều trị và phòng ngừa các bệnh lý phổ biến.',\n    instructor: 'ThS. Trần Thị B',\n    duration: '6 tuần',\n    students: 890,\n    price: '1.800.000đ',\n    originalPrice: '2.500.000đ',\n    image: '/assets/course2.jpg',\n    category: 'Dinh dưỡng',\n    rating: 4.9\n  },\n  {\n    id: '3',\n    title: 'Chăm Sóc Người Cao Tuổi',\n    description: 'Kỹ năng chăm sóc chuyên nghiệp cho người cao tuổi và các bệnh lý thường gặp.',\n    instructor: 'PGS.TS. Lê Văn C',\n    duration: '10 tuần',\n    students: 650,\n    price: '2.200.000đ',\n    originalPrice: '3.000.000đ',\n    image: '/assets/course3.jpg',\n    category: 'Chăm sóc đặc biệt',\n    rating: 4.7\n  },\n  {\n    id: '4',\n    title: 'Y Học Cổ Truyền',\n    description: 'Ứng dụng y học cổ truyền trong chăm sóc sức khỏe hiện đại.',\n    instructor: 'BS. Phạm Thị D',\n    duration: '12 tuần',\n    students: 420,\n    price: '1.900.000đ',\n    image: '/assets/course4.jpg',\n    category: 'Y học cổ truyền',\n    rating: 4.6\n  }\n];\n\nconst CourseCard = ({ course }: { course: Course }) => {\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group\">\n      {/* Course Image */}\n      <div className=\"relative h-48 overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center\">\n          <div className=\"text-white text-center\">\n            <div className=\"w-16 h-16 mx-auto mb-3 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <div className=\"text-sm font-medium\">{course.category}</div>\n          </div>\n        </div>\n        \n        {/* Discount Badge */}\n        {course.originalPrice && (\n          <div className=\"absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold\">\n            Giảm giá\n          </div>\n        )}\n        \n        {/* Rating */}\n        <div className=\"absolute top-3 right-3 bg-white bg-opacity-90 px-2 py-1 rounded-full flex items-center space-x-1\">\n          <svg className=\"w-4 h-4 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n          </svg>\n          <span className=\"text-sm font-medium\">{course.rating}</span>\n        </div>\n      </div>\n\n      {/* Course Content */}\n      <div className=\"p-6\">\n        <div className=\"mb-3\">\n          <span className=\"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium\">\n            {course.category}\n          </span>\n        </div>\n        \n        <h3 className=\"text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\">\n          {course.title}\n        </h3>\n        \n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {course.description}\n        </p>\n        \n        <div className=\"flex items-center text-sm text-gray-500 mb-4 space-x-4\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\n            </svg>\n            {course.instructor}\n          </div>\n          <div className=\"flex items-center\">\n            <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"/>\n              <path d=\"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z\"/>\n            </svg>\n            {course.duration}\n          </div>\n        </div>\n        \n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-2xl font-bold text-blue-600\">{course.price}</span>\n            {course.originalPrice && (\n              <span className=\"text-sm text-gray-400 line-through\">{course.originalPrice}</span>\n            )}\n          </div>\n          \n          <div className=\"text-sm text-gray-500\">\n            {course.students.toLocaleString()} học viên\n          </div>\n        </div>\n        \n        <Link\n          href={`/khoa-hoc/${course.id}`}\n          className=\"mt-4 w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 text-center block\"\n        >\n          Xem Chi Tiết\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nconst CourseSection = () => {\n  return (\n    <section className=\"py-16 lg:py-24 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n            Khóa Học Nổi Bật\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Khám phá các khóa học chất lượng cao được thiết kế bởi đội ngũ chuyên gia y tế hàng đầu\n          </p>\n        </div>\n\n        {/* Course Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12\">\n          {courses.map((course) => (\n            <CourseCard key={course.id} course={course} />\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <div className=\"text-center\">\n          <Link\n            href=\"/khoa-hoc\"\n            className=\"inline-flex items-center bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200\"\n          >\n            Xem Tất Cả Khóa Học\n            <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default CourseSection;\n"], "names": [], "mappings": ";;;;AAAA;;;AAiBA,MAAM,UAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;QACV,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;QACV,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;QACV,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ;IACV;CACD;AAED,MAAM,aAAa,CAAC,EAAE,MAAM,EAAsB;IAChD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;8CAAuB,OAAO,QAAQ;;;;;;;;;;;;;;;;;oBAKxD,OAAO,aAAa,kBACnB,8OAAC;wBAAI,WAAU;kCAA2F;;;;;;kCAM5G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA0B,MAAK;gCAAe,SAAQ;0CACnE,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;0CAEV,8OAAC;gCAAK,WAAU;0CAAuB,OAAO,MAAM;;;;;;;;;;;;;;;;;;0BAKxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACb,OAAO,QAAQ;;;;;;;;;;;kCAIpB,8OAAC;wBAAG,WAAU;kCACX,OAAO,KAAK;;;;;;kCAGf,8OAAC;wBAAE,WAAU;kCACV,OAAO,WAAW;;;;;;kCAGrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;oCAET,OAAO,UAAU;;;;;;;0CAEpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;;0DACxD,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;;;;;;;oCAET,OAAO,QAAQ;;;;;;;;;;;;;kCAIpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoC,OAAO,KAAK;;;;;;oCAC/D,OAAO,aAAa,kBACnB,8OAAC;wCAAK,WAAU;kDAAsC,OAAO,aAAa;;;;;;;;;;;;0CAI9E,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,QAAQ,CAAC,cAAc;oCAAG;;;;;;;;;;;;;kCAItC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE;wBAC9B,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;AAEA,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4BAA2B,QAAQ;2BAAnB,OAAO,EAAE;;;;;;;;;;8BAK9B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;4BACX;0CAEC,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACtE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;uCAEe", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/FeaturesSection.tsx"], "sourcesContent": ["const FeaturesSection = () => {\n  const features = [\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n        </svg>\n      ),\n      title: \"Chất <PERSON>\",\n      description: \"<PERSON><PERSON><PERSON><PERSON> họ<PERSON> đượ<PERSON> thiết kế bởi đội ngũ chuyên gia y tế hàng đầu với nhiều năm kinh nghiệm.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n        </svg>\n      ),\n      title: \"<PERSON>ứng Chỉ Uy Tín\",\n      description: \"<PERSON><PERSON>ận chứng chỉ được công nhận rộng rãi sau khi hoàn thành khóa học.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"/>\n          <path d=\"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z\"/>\n        </svg>\n      ),\n      title: \"Học Linh Hoạt\",\n      description: \"Học mọi lúc, mọi nơi với nền tảng trực tuyến 24/7. Tự điều chỉnh tốc độ học phù hợp.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z\"/>\n        </svg>\n      ),\n      title: \"Tài Liệu Phong Phú\",\n      description: \"Thư viện tài liệu đa dạng với video, bài giảng, và tài liệu tham khảo chuyên sâu.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\n        </svg>\n      ),\n      title: \"Hỗ Trợ Chuyên Gia\",\n      description: \"Đội ngũ hỗ trợ chuyên nghiệp sẵn sàng giải đáp mọi thắc mắc trong quá trình học.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19V1h-2v1H7V1H5v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2z\"/>\n        </svg>\n      ),\n      title: \"Cập Nhật Liên Tục\",\n      description: \"Nội dung khóa học được cập nhật thường xuyên theo xu hướng y tế mới nhất.\"\n    }\n  ];\n\n  return (\n    <section className=\"py-16 lg:py-24 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n            Tại Sao Chọn OME?\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Chúng tôi cam kết mang đến trải nghiệm học tập tốt nhất với những ưu điểm vượt trội\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"group p-8 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300\"\n            >\n              <div className=\"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center text-blue-600 mb-6 group-hover:bg-blue-600 group-hover:text-white transition-colors duration-300\">\n                {feature.icon}\n              </div>\n              \n              <h3 className=\"text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300\">\n                {feature.title}\n              </h3>\n              \n              <p className=\"text-gray-600 leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-8 lg:p-12 text-white\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold mb-4\">\n              Sẵn Sàng Bắt Đầu Hành Trình Học Tập?\n            </h3>\n            <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n              Tham gia cùng hàng nghìn học viên đã tin tưởng và đạt được thành công với OME\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200\">\n                Đăng Ký Ngay\n              </button>\n              <button className=\"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200\">\n                Tư Vấn Miễn Phí\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;;kCACnD,8OAAC;wBAAK,GAAE;;;;;;kCACR,8OAAC;wBAAK,GAAE;;;;;;;;;;;;YAGZ,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAGf,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAGhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAZjB;;;;;;;;;;8BAmBX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA6G;;;;;;kDAG/H,8OAAC;wCAAO,WAAU;kDAAwI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxK;uCAEe", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <Link href=\"/\" className=\"inline-block mb-6\">\n              <div className=\"w-32 h-10 relative\">\n                <Image\n                  src=\"/assets/logoome.svg\"\n                  alt=\"OME Logo\"\n                  fill\n                  className=\"object-contain brightness-0 invert\"\n                />\n              </div>\n            </Link>\n            \n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              Nền tảng học trực tuyến hàng đầu về chăm sóc sức khỏe. \n              C<PERSON> cấp kiến thức chuyên môn từ các chuyên gia y tế uy tín.\n            </p>\n            \n            <div className=\"flex space-x-4\">\n              <a\n                href=\"https://facebook.com\"\n                className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              \n              <a\n                href=\"https://youtube.com\"\n                className=\"w-10 h-10 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-colors\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                </svg>\n              </a>\n              \n              <a\n                href=\"https://linkedin.com\"\n                className=\"w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center hover:bg-blue-800 transition-colors\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Liên Kết Nhanh</h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/gioi-thieu\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Giới thiệu\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/khoa-hoc\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Khóa học\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/tin-tuc\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Tin tức\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/su-kien\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Sự kiện\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/lien-he\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Liên hệ\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Hỗ Trợ</h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/huong-dan\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Hướng dẫn học\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/cau-hoi-thuong-gap\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Câu hỏi thường gặp\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/chinh-sach\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Chính sách\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/dieu-khoan\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Điều khoản sử dụng\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/bao-mat\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Chính sách bảo mật\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Thông Tin Liên Hệ</h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start space-x-3\">\n                <svg className=\"w-5 h-5 text-blue-400 mt-1 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n                </svg>\n                <div>\n                  <p className=\"text-gray-300\">\n                    123 Đường ABC, Quận 1<br />\n                    TP. Hồ Chí Minh, Việt Nam\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <svg className=\"w-5 h-5 text-blue-400 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z\"/>\n                </svg>\n                <a href=\"tel:02899998899\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  028 9999 8899\n                </a>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <svg className=\"w-5 h-5 text-blue-400 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z\"/>\n                </svg>\n                <a href=\"mailto:<EMAIL>\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  <EMAIL>\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 text-sm\">\n              © 2024 OME Việt Nam. Tất cả quyền được bảo lưu.\n            </p>\n            <div className=\"flex space-x-6 mt-4 md:mt-0\">\n              <Link href=\"/chinh-sach\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n                Chính sách bảo mật\n              </Link>\n              <Link href=\"/dieu-khoan\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n                Điều khoản sử dụng\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAKlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,QAAO;4CACP,KAAI;sDAEJ,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,QAAO;4CACP,KAAI;sDAEJ,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,QAAO;4CACP,KAAI;sDAEJ,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAmD;;;;;;;;;;;sDAIxF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAmD;;;;;;;;;;;sDAItF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAmD;;;;;;;;;;;sDAIvF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAAmD;;;;;;;;;;;sDAIhG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAmD;;;;;;;;;;;sDAIxF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAmD;;;;;;;;;;;sDAIxF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA2C,MAAK;oDAAe,SAAQ;8DACpF,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,8OAAC;8DACC,cAAA,8OAAC;wDAAE,WAAU;;4DAAgB;0EACN,8OAAC;;;;;4DAAK;;;;;;;;;;;;;;;;;;sDAMjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAsC,MAAK;oDAAe,SAAQ;8DAC/E,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,8OAAC;oDAAE,MAAK;oDAAkB,WAAU;8DAAmD;;;;;;;;;;;;sDAKzF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAsC,MAAK;oDAAe,SAAQ;8DAC/E,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,8OAAC;oDAAE,MAAK;oDAAyB,WAAU;8DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxG,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAA2D;;;;;;kDAG9F,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5G;uCAEe", "debugId": null}}, {"offset": {"line": 1762, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/FloatingButtons.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/FloatingButtons.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/FloatingButtons.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 1774, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/FloatingButtons.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/FloatingButtons.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/FloatingButtons.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 1786, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1794, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/Header';\nimport HeroSection from '@/components/HeroSection';\nimport CourseSection from '@/components/CourseSection';\nimport FeaturesSection from '@/components/FeaturesSection';\nimport Footer from '@/components/Footer';\nimport FloatingButtons from '@/components/FloatingButtons';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main>\n        <HeroSection />\n        <CourseSection />\n        <FeaturesSection />\n      </main>\n      <Footer />\n      <FloatingButtons />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,UAAW;;;;;kCACZ,8OAAC,mIAAA,CAAA,UAAa;;;;;kCACd,8OAAC,qIAAA,CAAA,UAAe;;;;;;;;;;;0BAElB,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC,qIAAA,CAAA,UAAe;;;;;;;;;;;AAGtB", "debugId": null}}]}