{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigationItems = [\n    { href: '/', label: 'Trang chủ' },\n    { href: '/gioi-thieu', label: 'Giới thiệu' },\n    { href: '/khoa-hoc', label: 'Khóa học' },\n    { href: '/tin-tuc', label: 'Tin tức' },\n    { href: '/lien-he', label: '<PERSON>ên hệ' },\n    { href: '/su-kien', label: 'Sự kiện' },\n  ];\n\n  return (\n    <header className=\"bg-white shadow-md sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"w-32 h-10 lg:w-40 lg:h-12 relative\">\n              <Image\n                src=\"/assets/logoome.svg\"\n                alt=\"OME Logo\"\n                fill\n                className=\"object-contain\"\n                priority\n              />\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200\"\n              >\n                {item.label}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Phone and Cart */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <a\n              href=\"tel:02899998899\"\n              className=\"text-blue-600 font-semibold hover:text-blue-700 transition-colors\"\n            >\n              028 9999 8899\n            </a>\n            <Link href=\"/gio-hang\" className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\">\n              <svg\n                className=\"w-6 h-6 text-gray-700\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5H19M7 13v6a2 2 0 002 2h6a2 2 0 002-2v-6\"\n                />\n              </svg>\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            <svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"lg:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-3\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-gray-700 hover:text-blue-600 font-medium py-2 transition-colors duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <div className=\"pt-3 border-t border-gray-200\">\n                <a\n                  href=\"tel:02899998899\"\n                  className=\"text-blue-600 font-semibold block py-2\"\n                >\n                  028 9999 8899\n                </a>\n                <Link\n                  href=\"/gio-hang\"\n                  className=\"text-gray-700 hover:text-blue-600 font-medium py-2 transition-colors duration-200 flex items-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <svg\n                    className=\"w-5 h-5 mr-2\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5H19M7 13v6a2 2 0 002 2h6a2 2 0 002-2v-6\"\n                    />\n                  </svg>\n                  Giỏ hàng\n                </Link>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAK,OAAO;QAAY;QAChC;YAAE,MAAM;YAAe,OAAO;QAAa;QAC3C;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,IAAI;;;;;;;;;;sCAUpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAC/B,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE9B,cAAA,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAEP,2BACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;6FAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAQX,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;4CAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;GAjJM;KAAA;uCAmJS", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Workspace/Ome/ome-clone/src/components/FloatingButtons.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nconst FloatingButtons = () => {\n  const [showBackToTop, setShowBackToTop] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setShowBackToTop(window.scrollY > 300);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50 flex flex-col space-y-3\">\n      {/* <PERSON><PERSON> */}\n      <a\n        href=\"https://zalo.me/your-zalo-id\"\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n        className=\"w-14 h-14 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group\"\n        title=\"Chat qua Zalo\"\n      >\n        <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center\">\n          <svg className=\"w-5 h-5 text-blue-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z\"/>\n            <path d=\"M12 6c-3.31 0-6 2.69-6 6 0 1.66.67 3.16 1.76 4.24l-.71.71C6.34 16.24 6 14.66 6 13c0-3.31 2.69-6 6-6s6 2.69 6 6-2.69 6-6 6c-1.66 0-3.16-.34-4.24-1.05l-.71.71C8.84 19.33 10.34 20 12 20c3.31 0 6-2.69 6-6s-2.69-6-6-6z\"/>\n          </svg>\n        </div>\n        \n        {/* Tooltip */}\n        <div className=\"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\">\n          Chat qua Zalo\n          <div className=\"absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-800\"></div>\n        </div>\n      </a>\n\n      {/* Phone Button */}\n      <a\n        href=\"tel:+02899998899\"\n        className=\"w-14 h-14 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group\"\n        title=\"Gọi điện thoại\"\n      >\n        <svg className=\"w-7 h-7 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z\"/>\n        </svg>\n        \n        {/* Tooltip */}\n        <div className=\"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\">\n          028 9999 8899\n          <div className=\"absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-800\"></div>\n        </div>\n      </a>\n\n      {/* Back to Top Button */}\n      {showBackToTop && (\n        <button\n          onClick={scrollToTop}\n          className=\"w-14 h-14 bg-gray-700 hover:bg-gray-800 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group animate-fade-in\"\n          title=\"Về đầu trang\"\n        >\n          <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\n          </svg>\n          \n          {/* Tooltip */}\n          <div className=\"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\">\n            Về đầu trang\n            <div className=\"absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-800\"></div>\n          </div>\n        </button>\n      )}\n    </div>\n  );\n};\n\nexport default FloatingButtons;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,kBAAkB;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;0DAAe;oBACnB,iBAAiB,OAAO,OAAO,GAAG;gBACpC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,MAAK;gBACL,QAAO;gBACP,KAAI;gBACJ,WAAU;gBACV,OAAM;;kCAEN,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAe,SAAQ;;8CACjE,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;;4BAAmN;0CAEhO,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,OAAM;;kCAEN,6LAAC;wBAAI,WAAU;wBAAqB,MAAK;wBAAe,SAAQ;kCAC9D,cAAA,6LAAC;4BAAK,GAAE;;;;;;;;;;;kCAIV,6LAAC;wBAAI,WAAU;;4BAAmN;0CAEhO,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;YAKlB,+BACC,6LAAC;gBACC,SAAS;gBACT,WAAU;gBACV,OAAM;;kCAEN,6LAAC;wBAAI,WAAU;wBAAqB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC5E,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAIvE,6LAAC;wBAAI,WAAU;;4BAAmN;0CAEhO,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAhFM;KAAA;uCAkFS", "debugId": null}}]}